import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    IconButton,
    Typography,
    Box,
    Divider,
} from '@vapor/react-material';
import { Close } from '@mui/icons-material';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { renderCalendarText } from '../helpers/calendarHelper';

interface IMoreEventsModalProps {
    open: boolean;
    onClose: () => void;
    events: any[];
    date: Date | null;
    query?: any;
    loggedUserCampiAgenda?: any;
    impegniMultiutente?: any;
    netlexSettingsFileId?: any;
}

export default function MoreEventsModal(props: IMoreEventsModalProps) {
    const {
        open,
        onClose,
        events,
        date,
        query,
        loggedUserCampiAgenda,
        impegniMultiutente,
        netlexSettingsFileId
    } = props;

    const formatDate = (date: Date) => {
        return format(date, 'd MMMM yyyy', { locale: it });
    };

    const formatEventTime = (event: any) => {
        if (event.allDay) {
            return '';
        }
        if (event.start) {
            return format(new Date(event.start), 'HH:mm');
        }
        return '';
    };

    const getEventTypeColor = (type: string): { box: string, text: string } => {
        switch (type) {
            case "hearing":
                return {
                    box: '#AD3A00',
                    text: '#521B00'
                };
            case "deadline":
                return {
                    box: '#51822B',
                    text: '#263D14'
                };
            case "polisweb":
                return {
                    box: '#2196F3',
                    text: '#0A3C47'
                };
            default:
                return {
                    box: '#757575',
                    text: '#424242'
                };
        }
    };

    const getEventDisplayData = (event: any) => {
        console.log('Event in modal:', event);

        let backgroundColor = event.backgroundColor || event.color;

        if (!backgroundColor) {
            const eventType = event.type || event.extendedProps?.type || 'default';
            const colors = getEventTypeColor(eventType);
            backgroundColor = colors.box;
        }

        if (query && loggedUserCampiAgenda !== undefined && impegniMultiutente !== undefined && netlexSettingsFileId !== undefined) {
            try {
                const eventInfo = {
                    event: {
                        _def: {
                            title: event.title,
                            extendedProps: event.extendedProps || event,
                            allDay: event.allDay
                        }
                    }
                };
                const renderData = renderCalendarText(
                    eventInfo,
                    query.calendarReferent,
                    loggedUserCampiAgenda,
                    impegniMultiutente,
                    netlexSettingsFileId
                );

                console.log('Background color used:', backgroundColor);

                return {
                    title: event.title,
                    time: formatEventTime(event),
                    backgroundColor: backgroundColor,
                    textColor: 'white',
                    icons: renderData.icons || '',
                    type: event.type || event.extendedProps?.type || 'default'
                };
            } catch (error) {
                console.warn('Error using centralized rendering:', error);
            }
        }

        return {
            title: event.title,
            time: formatEventTime(event),
            backgroundColor: backgroundColor,
            textColor: 'white',
            icons: '',
            type: event.type || event.extendedProps?.type || 'default'
        };
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="sm"
            aria-labelledby="more-events-dialog-title"
            PaperProps={{
                sx: {
                    width: '30vw',
                    maxWidth: '90vw',
                    maxHeight:"50vh"
                }
            }}
        >
            <DialogTitle id="more-events-dialog-title" sx={{ py: 1.5, px: 2 }}>
                    <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                        {date ? formatDate(date) : 'Eventi'}
                    </Typography>
                    <IconButton
                        aria-label="close"
                        onClick={onClose}
                        size="medium"
                    >
                        <Close fontSize="small" />
                    </IconButton>
            </DialogTitle>

            <Divider />

            <DialogContent sx={{ p: 0 }}>
                <Box sx={{ width: '100%' }}>
                    {events.map((event, index) => {
                        const eventData = getEventDisplayData(event);
                        return (
                            <Box
                                key={event.id || index}
                                sx={{
                                    backgroundColor: eventData.backgroundColor || '#f5f5f5',
                                    color: 'white',
                                    p: 1.5,
                                    mb: index < events.length - 1 ? 0.5 : 0,
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5,
                                    cursor: 'pointer',
                                    minHeight: '40px',
                                    '&:hover': {
                                        opacity: 0.8,
                                    },
                                }}
                            >
                                {eventData.icons && (
                                    <Box
                                        sx={{ mr: 0.5 }}
                                        dangerouslySetInnerHTML={{ __html: eventData.icons }}
                                    />
                                )}

                                {eventData.time && (
                                    <Typography
                                        variant="caption"
                                        sx={{
                                            fontWeight: 'bold',
                                            mr: 0.5,
                                            minWidth: 'fit-content',
                                            fontSize: '0.75rem'
                                        }}
                                    >
                                        {eventData.time}
                                    </Typography>
                                )}

                                <Typography
                                    variant="body2"
                                    sx={{
                                        fontStyle: 'italic',
                                        flex: 1,
                                        fontSize: '0.875rem'
                                    }}
                                >
                                    {eventData.title}
                                </Typography>
                            </Box>
                        );
                    })}
                </Box>
            </DialogContent>

            <DialogActions sx={{ p: 1.5 }}>
                <Button onClick={onClose} variant="outlined" size="small">
                    Chiudi
                </Button>
            </DialogActions>
        </Dialog>
    );
}
