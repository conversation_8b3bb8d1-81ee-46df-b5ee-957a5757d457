import {
    Box,
    Grid,
    Typography,
    Divider
} from "@vapor/react-material";
import { Divider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import { faRepeat } from "@fortawesome/free-solid-svg-icons";
// import { faLock } from "@fortawesome/pro-regular-svg-icons";
import ClearRoundedIcon from '@mui/icons-material/ClearRounded';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';

interface IDayEventsProps {
    selectedDate: Date | null;
    events: any[];
    setRightPanelOpen: (open: boolean) => void;
    t: (key: string) => string;
}

export default function DayEvents(props: IDayEventsProps) {
    const { selectedDate, events, setRightPanelOpen, t } = props;

    const handleClose = () => {
        setRightPanelOpen(false);
    };

    const formatEventTime = (date: Date) => {
        return format(date, 'HH:mm');
    };

    const formatDateWithCapital = (date: Date) => {
        const formatted = format(date, 'EEE d', { locale: it });
        return formatted.charAt(0).toUpperCase() + formatted.slice(1);
    };

    const getEventTypeColor = (type: string): { box: string, text: string, background: string } => {
        switch (type) {
            case "hearing":
                return {
                    box: '#AD3A00',
                    text: '#521B00',
                    background: '#FFE0D1'
                };
            case "deadline":
                return {
                    box: '#53832D',
                    text: '#263D14',
                    background: '#E7F4DD'
                };
            case "polisweb":
                return {
                    box: '#2B8BA1',
                    text: '#0A3C47',
                    background: '#D7F3F9'
                };
            default:
                return {
                    box: '#757575',
                    text: '#424242',
                    background: '#F5F5F5'
                };
        }
    };

    const truncateText = (text: string, maxLength: number) => {
        if (text && text.length > maxLength) {
            return text.slice(0, maxLength) + '...';
        }
        return text;
    };

    return (
        <Box sx={{ p: 1, height: '80vh' }}>
            <Grid container spacing={2}>
                {/* Header with date and close button */}
                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography sx={{ fontSize: '22px', fontWeight: 'bold' }}>
                        {selectedDate && formatDateWithCapital(selectedDate)}
                    </Typography>
                    <ClearRoundedIcon onClick={handleClose} fontSize="medium" sx={{ color: '#008fd6' }} />
                </Grid>

                <Grid item xs={12}>
                    <Divider />
                </Grid>

                {/* Events list */}
                <Grid item xs={12}>
                    {events.map((event, index) => (
                        <>
                            <Box
                                key={event.id || index}
                                sx={{
                                    position: 'relative',
                                    mb: 0.5,
                                    p: 1.5,
                                    height: '100px',
                                    borderRadius: 1,
                                    backgroundColor: getEventTypeColor(event.type).background,
                                    borderLeft: `4px solid ${getEventTypeColor(event.type).box}`,
                                    borderTopLeftRadius: 4,
                                    borderBottomLeftRadius: 4,
                                    '&:hover': {
                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                    },
                                    ...(event.important !== "0" && {
                                        '&::after': {
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            width: 0,
                                            height: 0,
                                            borderStyle: 'solid',
                                            borderWidth: '0 15px 15px 0',
                                            borderColor: 'transparent #D10000 transparent transparent',
                                        }
                                    })
                                }}
                            >
                                {/* Add icons container */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: 8,
                                        right: 20,
                                        display: 'flex',
                                        gap: '4px',
                                        zIndex: 1
                                    }}
                                >
                                    {/* <FontAwesomeIcon icon={faRepeat} style={{ fontSize: 18, color: getEventTypeColor(event.type).text }} />
                                    <FontAwesomeIcon icon={faLock} style={{ fontSize: 18, color: getEventTypeColor(event.type).text }} /> */}
                                </Box>

                                <Grid container>
                                    <Grid item xs={2}>
                                        <Typography color="textSecondary" sx={{ fontSize: '12px', color: getEventTypeColor(event.type).text }}>
                                            {formatEventTime(new Date(event.start))}
                                        </Typography>
                                        <Typography color="textSecondary" sx={{ fontSize: '12px', color: getEventTypeColor(event.type).text }}>
                                            {event.durata} ore
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={10}>
                                        <Typography sx={{ fontWeight: 'bold', fontSize: '14px', color: getEventTypeColor(event.type).text }}>
                                            {truncateText(event.title, 20)}
                                        </Typography>
                                        {event.autorita && (
                                            <Typography color="textSecondary" sx={{ fontSize: '12px', color: getEventTypeColor(event.type).text }}>
                                                {truncateText(event.autorita, 25)}
                                            </Typography>
                                        )}
                                    </Grid>
                                    <Grid item xs={12}>
                                        <Box sx={{ display: 'flex', justifyContent: 'flex-start', pt: 1 }}>
                                            <Typography color="textSecondary" sx={{
                                                bgcolor: getEventTypeColor(event.type).box,
                                                fontSize: '14px',
                                                padding: '2px 8px',
                                                borderRadius: '4px',
                                                color: 'white',
                                            }}>
                                                {event.type === "deadline" ? event.tipologia : event.type}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                            <MuiDivider sx={{
                                my: 1,
                            }} />
                        </>
                    ))}

                    {events.length === 0 && (
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '100%',
                            pt: 4
                        }}>
                            <FormatListBulletedIcon
                                sx={{
                                    fontSize: 45,
                                    mb: 3,
                                    mt: 2
                                }}
                            />
                            <Typography
                                variant="body1"
                                sx={{
                                    fontWeight: "bold",
                                    textAlign: 'center',
                                    fontSize: '18px'
                                }}
                            >
                                {t("Nessun impegno da visualizzare")}
                            </Typography>
                        </Box>
                    )}
                </Grid>
            </Grid>
        </Box>
    );
} 