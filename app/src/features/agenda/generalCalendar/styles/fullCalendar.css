.fc-day {
    color: hsl(200, 100%, 23%);
    font-family: "Cairo";
}
.fc .fc-col-header-cell-cushion {
    text-transform: capitalize;
    font-family: "Cairo";
    color: hsla(200, 100%, 23%, 1);
}
.fc-toolbar-title {
    text-transform: capitalize;
}


/* Only apply fixed height to month view (dayGrid), not week/day views */
.fc-dayGridMonth-view .fc-daygrid-day-frame {
    min-height: 120px; /* Minimum height for each day cell */
    height: 120px !important; /* Fixed height to prevent row expansion */
    max-height: 120px !important;
    overflow: hidden;
}

.fc-day-other {
    opacity: 1;
    background-color: #ffffff;
}

.fc-day-other .fc-daygrid-day-number {
    color: hsl(200, 100%, 23%);
    font-weight: 500;
    font-size: 1em;
}

.fc-day {
    display: table-cell !important;
    visibility: visible !important;
}

.fc-daygrid-day-number {
    display: block !important;
    visibility: visible !important;
}



.fc-view-harness {
    height: auto !important;
}

/* Only apply fixed height to month view week rows */
.fc-dayGridMonth-view .fc-daygrid-week {
    min-height: 120px;
    height: 120px !important; /* Fixed height for week rows */
    max-height: 120px !important; /* Prevent expansion */
}

/* Ensure event containers within day cells respect fixed height - month view only */
.fc-dayGridMonth-view .fc-daygrid-day-events {
    height: auto !important;
    max-height: 90px !important; /* Leave space for day number */
    overflow: hidden !important;
}

/* Ensure day cells maintain fixed height - month view only */
.fc-dayGridMonth-view .fc-daygrid-day {
    height: 120px !important;
    max-height: 120px !important;
    overflow: hidden !important;
}

/* Fix table row height in month view only */
.fc-dayGridMonth-view .fc-daygrid-body tr {
    height: 120px !important;
    max-height: 120px !important;
}

/* Ensure table cells maintain fixed height - month view only */
.fc-dayGridMonth-view .fc-daygrid-body td {
    height: 120px !important;
    max-height: 120px !important;
    vertical-align: top !important;
    overflow: hidden !important;
}

.fc-day:not(.fc-day-other) {
    background-color: #ffffff;
    opacity: 1;
}

.fc-day:not(.fc-day-other) .fc-daygrid-day-number {
    color: hsl(200, 100%, 23%);
    font-weight: 500;
}


.fc-day-today {
    background-color: #ffffff !important;
}

.fc-day-today .fc-daygrid-day-number {
    background-color: #005075 !important;
    color: #ffffff !important;
    font-weight: bold !important;
    border-radius: 50% !important;
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 2px !important;
}

.fc-col-header-cell {
    background-color: #f5f5f5;
    border-bottom: 2px solid #dee2e6;
}



.fc-col-header-cell-cushion {
    padding: 8px 4px;
    font-weight: 600;
    text-transform: capitalize;
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.2;
}

/* Fix overlapping text issue in week view headers */
.fc-timeGridWeek-view .fc-col-header-cell-cushion {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    gap: 2px !important;
    padding: 8px 4px !important;
    position: relative !important;
}

.fc-timeGridWeek-view .fc-col-header-cell-cushion div:first-child {
    font-size: 1.1em !important;
    font-weight: bold !important;
    line-height: 1 !important;
    margin: 0 !important;
}

.fc-timeGridWeek-view .fc-col-header-cell-cushion div:last-child {
    font-size: 0.9em !important;
    font-weight: normal !important;
    text-transform: capitalize !important;
    line-height: 1 !important;
    margin: 0 !important;
}

/* Hide default FullCalendar header content in week view to prevent overlap */
.fc-timeGridWeek-view .fc-col-header-cell-cushion .fc-col-header-cell-text {
    display: none !important;
}

/* Ensure no other default content interferes */
.fc-timeGridWeek-view .fc-col-header-cell-cushion > *:not(div) {
    display: none !important;
}

/* Make sure the header cell has proper height */
.fc-timeGridWeek-view .fc-col-header-cell {
    height: auto !important;
    min-height: 60px !important;
}

/* Ensure full week shows all 7 columns properly */
.fc-timeGridWeek-view .fc-scrollgrid {
    width: 100% !important;
}

.fc-timeGridWeek-view .fc-col-header-cell {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

.fc-timeGridWeek-view .fc-timegrid-col {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

/* Work Week Mode Styles (when weekends are hidden) - Layout */
.fc-timeGridWeek-view:not(.fc-weekends) .fc-col-header-cell {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

.fc-timeGridWeek-view:not(.fc-weekends) .fc-timegrid-col {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

.fc-timegrid .fc-col-header-cell-cushion {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 2px;
}

.fc-timegrid .fc-col-header-cell-cushion div:first-child {
    font-size: 1.1em;
    font-weight: bold;
}

.fc-timegrid .fc-col-header-cell-cushion div:last-child {
    font-size: 0.9em;
    font-weight: normal;
    text-transform: capitalize;
}



.fc-timegrid-divider {
    display: none !important;
}

/* Today indicator for week views - header cell border for both timeGridWeek and timeGridWorkWeek */
.fc-timeGridWeek-view .fc-col-header-cell.fc-day-today,
.fc-timeGridWorkWeek-view .fc-col-header-cell.fc-day-today {
    padding-bottom: 4px !important;
    box-shadow: inset 0 -4px 0 #005075 !important;
}

/* Alternative selector in case the view class is different */
.fc-timegrid .fc-col-header-cell.fc-day-today {
    box-shadow: inset 0 -4px 0 #005075 !important;
}

/* Remove borders between columns in all-day event row */
.fc-timegrid-allday-slot .fc-timegrid-col {
    border-left: none !important;
    border-right: none !important;
}

/* Remove borders from all-day slot area */
.fc-timegrid-allday-slot {
    border-left: none !important;
    border-right: none !important;
}

/* Ensure all-day slot in week/day views has normal height (not affected by month view rules) */
.fc-timeGridWeek-view .fc-timegrid-allday-slot,
.fc-timeGridWorkWeek-view .fc-timegrid-allday-slot,
.fc-timeGridDay-view .fc-timegrid-allday-slot {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Ensure all-day event containers have normal height */
.fc-timeGridWeek-view .fc-timegrid-allday,
.fc-timeGridWorkWeek-view .fc-timegrid-allday,
.fc-timeGridDay-view .fc-timegrid-allday {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Ensure all-day columns have normal height */
.fc-timeGridWeek-view .fc-timegrid-allday .fc-timegrid-col,
.fc-timeGridWorkWeek-view .fc-timegrid-allday .fc-timegrid-col,
.fc-timeGridDay-view .fc-timegrid-allday .fc-timegrid-col {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Remove borders from all-day event containers */
.fc-timegrid-allday .fc-timegrid-col {
    border-left: none !important;
    border-right: none !important;
}

/* Ensure first and last columns don't have borders either */
.fc-timegrid-allday-slot .fc-timegrid-col:first-child,
.fc-timegrid-allday-slot .fc-timegrid-col:last-child {
    border-left: none !important;
    border-right: none !important;
}

/* Make time slots expand to fill available height */
.fc-timegrid-slots {
    height: 100% !important;
}

/* Ensure time slots have flexible height */
.fc-timegrid-slot {
    height: auto !important;
    min-height: 2em !important;
    flex: 1 1 auto !important;
}

/* Specific styles for day view to make hour slots expand */
.fc-timeGridDay-view .fc-timegrid-slot {
    height: auto !important;
    min-height: 3em !important;
    flex: 1 1 auto !important;
}

/* Ensure the timegrid body expands to fill available space */
.fc-timegrid-body {
    height: 100% !important;
    flex: 1 1 auto !important;
}

/* Make the scrollgrid expand properly */
.fc-timegrid .fc-scrollgrid {
    height: 100% !important;
}

/* Ensure the time axis and slots container expand */
.fc-timegrid-axis-chunk,
.fc-timegrid-slots-chunk {
    height: 100% !important;
}

/* Make the main time area expand */
.fc-timegrid-time-area {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Ensure slots table expands */
.fc-timegrid-slots table {
    height: 100% !important;
}

/* More Events Link Styling (+3 button) */
.fc-more-link,
.fc-daygrid-more-link {
    background-color: #E5E8EB !important;
    color: #333333 !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    text-align: center !important;
    margin: 2px 0 !important;
    cursor: pointer !important;
}

.fc-more-link:hover,
.fc-daygrid-more-link:hover {
    background-color: #d1d5db !important;
    color: #333333 !important;
}

/* Ensure more link works in both day grid and time grid views */
.fc-daygrid .fc-more-link,
.fc-timegrid .fc-more-link {
    background-color: #E5E8EB !important;
    color: #333333 !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    text-align: center !important;
    margin: 2px 0 !important;
    cursor: pointer !important;
}

/* Hide FullCalendar popovers since we use custom modal */
.fc-popover,
.fc-more-popover {
    display: none !important;
}

/* Allow side-by-side events while preventing visual overlap */
.fc-timegrid-event-harness {
    margin: 1px !important;
}

/* Ensure events have proper spacing and borders when side by side */
.fc-timegrid-event {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-sizing: border-box !important;
}

/* Remove the global 4px border rule - we'll be more specific */

/* Specific event type styling for all views */
.fc-event[data-event-type="hearing"],
.fc-daygrid-event[data-event-type="hearing"],
.fc-timegrid-event[data-event-type="hearing"],
.fc-list-event[data-event-type="hearing"] {
    background-color: #FFE0D1 !important;
    border-left-color: #AD3A00 !important;
}

.fc-event[data-event-type="deadline"],
.fc-daygrid-event[data-event-type="deadline"],
.fc-timegrid-event[data-event-type="deadline"],
.fc-list-event[data-event-type="deadline"] {
    background-color: #E7F4DD !important;
    border-left-color: #53832D !important;
}

.fc-event[data-event-type="polisweb"],
.fc-daygrid-event[data-event-type="polisweb"],
.fc-timegrid-event[data-event-type="polisweb"],
.fc-list-event[data-event-type="polisweb"] {
    background-color: #D7F3F9 !important;
    border-left-color: #2B8BA1 !important;
}

/* Additional styling for timegrid events to ensure proper borders */
.fc-timegrid-event-harness .fc-event {
    border-left-width: 4px !important;
}

/* Force border styling for all event elements */
.fc-event-main {
    border-left: inherit !important;
}

/* Ensure event content respects the border */
.fc-event-title,
.fc-event-time {
    padding-left: 8px !important;
}

/* Alternative approach: Style events based on background color */
/* Hearing events (orange background) */
.fc-event[style*="background-color: rgb(255, 224, 209)"],
.fc-event[style*="background-color:#FFE0D1"],
.fc-timegrid-event[style*="background-color: rgb(255, 224, 209)"],
.fc-timegrid-event[style*="background-color:#FFE0D1"],
.fc-daygrid-event[style*="background-color: rgb(255, 224, 209)"],
.fc-daygrid-event[style*="background-color:#FFE0D1"] {
    border-left: 4px solid #AD3A00 !important;
}

/* Deadline events (green background) */
.fc-event[style*="background-color: rgb(231, 244, 221)"],
.fc-event[style*="background-color:#E7F4DD"],
.fc-timegrid-event[style*="background-color: rgb(231, 244, 221)"],
.fc-timegrid-event[style*="background-color:#E7F4DD"],
.fc-daygrid-event[style*="background-color: rgb(231, 244, 221)"],
.fc-daygrid-event[style*="background-color:#E7F4DD"] {
    border-left: 4px solid #53832D !important;
}

/* Polisweb events (blue background) */
.fc-event[style*="background-color: rgb(215, 243, 249)"],
.fc-event[style*="background-color:#D7F3F9"],
.fc-timegrid-event[style*="background-color: rgb(215, 243, 249)"],
.fc-timegrid-event[style*="background-color:#D7F3F9"],
.fc-daygrid-event[style*="background-color: rgb(215, 243, 249)"],
.fc-daygrid-event[style*="background-color:#D7F3F9"] {
    border-left: 4px solid #2B8BA1 !important;
}

/* Only apply 4px borders to our specific event types */



