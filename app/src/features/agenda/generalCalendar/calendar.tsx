import "./styles/fullCalendar.css";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";
import itLocale from "@fullcalendar/core/locales/it";
import { useUser } from "../../../store/UserStore";
import { CustomEventUI } from "./helpers/customUIEvents";
import CalendarNavigationButtons from "./helpers/CalendarNavigationButtons";
import CalendarActionButtons from "./helpers/CalendarActionButtons";
import { ICalendarProps, ICalendarEvent } from "./typings/generalCalendar.interface";
import { useHandleEventDrop } from "./hooks/useHandleEventDrop";
import { useNavigate, useLocation } from "react-router-dom";
import { manageDeadlinesNotice, manageHearingsNotice } from "./utils";
import { gettingCalendarView, gettingCalendarViewName } from "./helpers/gettingCalendarViewName";
import { useEffect, useState } from "react";
import { Grid } from "@mui/material";
import moment from "moment";
import Filters from "./filters";
import DayEvents from './components/DayEvents';
import MoreEventsModal from './components/MoreEventsModal';

const parseQueryDate = (dateValue: number) => {
    if (!dateValue || dateValue === 0) return moment();
    if (dateValue > 1000000000000) {
        return moment(dateValue);
    } else {
        return moment.unix(dateValue);
    }
};

function addEndTimeToEvents(events: ICalendarEvent[]): any[] {
    if (!events || !Array.isArray(events)) {
        return [];
    }

    const getEventTypeColor = (type: string): { box: string, text: string, background: string } => {
        switch (type) {
            case "hearing":
                return {
                    box: '#AD3A00',
                    text: '#521B00',
                    background: '#FFE0D1'
                };
            case "deadline":
                return {
                    box: '#53832D',
                    text: '#263D14',
                    background: '#E7F4DD'
                };
            case "polisweb":
                return {
                    box: '#2B8BA1',
                    text: '#0A3C47',
                    background: '#D7F3F9'
                };
            default:
                return {
                    box: '#757575',
                    text: '#424242',
                    background: '#F5F5F5'
                };
        }
    };

    return events.map((event: ICalendarEvent) => {
        const colors = getEventTypeColor(event.type);
        const eventWithColors = {
            ...event,
            backgroundColor: colors.background,
            borderColor: colors.box,
        };

        if (!(event as any).end && event.start) {
            const startDate = new Date(event.start);
            const endDate = new Date(
                startDate.getTime() + Number(event.durata) * 60000
            );
            return {
                ...eventWithColors,
                start: event.start, // Keep original start format
                end: endDate,
            };
        }

        return eventWithColors;
    });
}

const validateEvents = (events: ICalendarEvent[]): ICalendarEvent[] => {
    if (!Array.isArray(events)) return [];

    return events.filter(event => {
        if (!event || typeof event !== 'object') return false;
        if (!event.start) return false;
        try {
            const startDate = new Date(event.start);
            if (isNaN(startDate.getTime())) return false;
        } catch (error) {
            return false;
        }
        if ((event as any).end) {
            try {
                const endDate = new Date((event as any).end);
                if (isNaN(endDate.getTime())) {
                    delete (event as any).end;
                }
            } catch (error) {
                delete (event as any).end;
            }
        }

        return true;
    });
};


const processEventData = (eventData: ICalendarEvent[]): any[] => {
    const safeEventData = validateEvents(eventData);
    let updatedEvents = manageDeadlinesNotice(safeEventData);
    updatedEvents = manageHearingsNotice(updatedEvents);
    updatedEvents = addEndTimeToEvents(updatedEvents);

    // Add test events with overlapping times
    const today = new Date();
    const testDate = new Date(today.getFullYear(), today.getMonth(), today.getDate()); // Today

    const testEvents = [
        {
            id: 'test-hearing-static-1',
            title: 'Call cliente',
            type: 'hearing',
            start: new Date(testDate.getFullYear(), testDate.getMonth(), testDate.getDate(), 14, 0).toISOString(),
            durata: '90',
            ora: '14:00',
            evasa: '1',
            polisweb: '0',
            annotazioni: 'portare il proprio materiale',
            uniqueid: 'test-hearing-1',
            modificatoil: new Date().toISOString(),
            avviso: null,
            ggmancanti: null,
            important: '0',
            danonevadere: '0',
            dataUdienza: testDate.toISOString(),
            dataUltimaUdienza: testDate.toISOString(),
            status: '0',
            className: 'test-event test-hearing-static',
            editable: '1',
            color: '#FFE0D1',
            backgroundColor: '#FFE0D1',
            borderColor: '#AD3A00',
            pratica: null,
            contract_id: null,
            descrizionepratica: null,
            nome_pratica: null,
            ruologeneralenumero: null,
            ruologeneraleanno: null,
            subprocedimento: null,
            rgnr: null,
            rgnranno: null,
            rggip: null,
            rggipanno: null,
            rggup: null,
            rggupanno: null,
            rgtrib: null,
            rgtribanno: null,
            rgapp: null,
            rgappanno: null,
            rgcass: null,
            rgcassanno: null,
            rgsiep: null,
            rgsiepanno: null,
            rgsius: null,
            rgsiusanno: null,
            rgriesame: null,
            rgriesameanno: null,
            nomeStatoPratica: 'Test',
            avvocato: 'Test Lawyer',
            tipologia: 'Test Type',
            citta: null,
            autorita: 'Tribunale di Roma, via Tribunale',
            sezione: null,
            istruttore: null,
            codicearchivio: null,
            fileUid: null,
            dinamica: null,
            fkUtenteGruppo: null,
            nomepratica: null,
            calendarioEsternoType: null,
            isOld: '0',
            listaclienti: null,
            listacontroparti: null,
            nomeutente: 'Test User',
            sigla: null,
            referenti: 'Test Referent',
            subject: 'Test Subject'
        },
        {
            id: 'test-deadline-static-1',
            title: 'STATIC TEST: Deadline Event',
            type: 'deadline',
            start: new Date(testDate.getFullYear(), testDate.getMonth(), testDate.getDate(), 14, 30).toISOString(),
            durata: '120',
            ora: '14:30',
            evasa: '0',
            polisweb: '0',
            annotazioni: 'Test deadline annotation',
            uniqueid: 'test-deadline-1',
            modificatoil: new Date().toISOString(),
            avviso: null,
            ggmancanti: null,
            important: '0',
            danonevadere: '0',
            dataUdienza: null,
            dataUltimaUdienza: '',
            status: '0',
            className: 'test-event test-deadline-static',
            editable: '1',
            color: '#E7F4DD',
            backgroundColor: '#E7F4DD',
            borderColor: '#53832D',
            pratica: null,
            contract_id: null,
            descrizionepratica: null,
            nome_pratica: null,
            ruologeneralenumero: null,
            ruologeneraleanno: null,
            subprocedimento: null,
            rgnr: null,
            rgnranno: null,
            rggip: null,
            rggipanno: null,
            rggup: null,
            rggupanno: null,
            rgtrib: null,
            rgtribanno: null,
            rgapp: null,
            rgappanno: null,
            rgcass: null,
            rgcassanno: null,
            rgsiep: null,
            rgsiepanno: null,
            rgsius: null,
            rgsiusanno: null,
            rgriesame: null,
            rgriesameanno: null,
            nomeStatoPratica: 'Test',
            avvocato: 'Test Lawyer',
            tipologia: 'Test Deadline Type',
            citta: null,
            autorita: 'Test Authority',
            sezione: null,
            istruttore: null,
            codicearchivio: null,
            fileUid: null,
            dinamica: null,
            fkUtenteGruppo: null,
            nomepratica: null,
            calendarioEsternoType: null,
            isOld: '0',
            listaclienti: null,
            listacontroparti: null,
            nomeutente: 'Test User',
            sigla: null,
            referenti: 'Test Referent',
            subject: 'Test Subject'
        },
        {
            id: 'test-polisweb-static-1',
            title: 'STATIC TEST: Polisweb Event',
            type: 'polisweb',
            start: new Date(testDate.getFullYear(), testDate.getMonth(), testDate.getDate(), 15, 0).toISOString(),
            durata: '60',
            ora: '15:00',
            evasa: '0',
            polisweb: '1',
            annotazioni: 'Test polisweb annotation',
            uniqueid: 'test-polisweb-1',
            modificatoil: new Date().toISOString(),
            avviso: null,
            ggmancanti: null,
            important: '0',
            danonevadere: '0',
            dataUdienza: null,
            dataUltimaUdienza: '',
            status: '0',
            className: 'test-event test-polisweb-static',
            editable: '1',
            color: '#D7F3F9',
            backgroundColor: '#D7F3F9',
            borderColor: '#2B8BA1',
            pratica: null,
            contract_id: null,
            descrizionepratica: null,
            nome_pratica: null,
            ruologeneralenumero: null,
            ruologeneraleanno: null,
            subprocedimento: null,
            rgnr: null,
            rgnranno: null,
            rggip: null,
            rggipanno: null,
            rggup: null,
            rggupanno: null,
            rgtrib: null,
            rgtribanno: null,
            rgapp: null,
            rgappanno: null,
            rgcass: null,
            rgcassanno: null,
            rgsiep: null,
            rgsiepanno: null,
            rgsius: null,
            rgsiusanno: null,
            rgriesame: null,
            rgriesameanno: null,
            nomeStatoPratica: 'Test',
            avvocato: 'Test Lawyer',
            tipologia: 'Test Polisweb Type',
            citta: null,
            autorita: 'Test Authority',
            sezione: null,
            istruttore: null,
            codicearchivio: null,
            fileUid: null,
            dinamica: null,
            fkUtenteGruppo: null,
            nomepratica: null,
            calendarioEsternoType: null,
            isOld: '0',
            listaclienti: null,
            listacontroparti: null,
            nomeutente: 'Test User',
            sigla: null,
            referenti: 'Test Referent',
            subject: 'Test Subject'
        }
    ];

    const fullDayEventWithAllDay = {
        allDay: true
    };

    return [...updatedEvents, ...testEvents, fullDayEventWithAllDay];
};

const getValidatedEventsForCalendar = (updatedEvents: any[]): any[] => {
    try {
        const validatedEvents = Array.isArray(updatedEvents) ? updatedEvents : [];
        return validatedEvents.filter(event => {
            return event && event.start && !isNaN(new Date(event.start).getTime());
        });
    } catch (error) {
        return [];
    }
};

const sortEventsForDisplay = (events: any[], currentView: string): any[] => {
    // For monthly view, prioritize full-day events to show them first
    if (currentView === 'month' || currentView === 'dayGridMonth') {
        return [...events].sort((a, b) => {
            // Full-day events first
            if (a.allDay && !b.allDay) return -1;
            if (!a.allDay && b.allDay) return 1;

            // If both are full-day or both are not full-day, sort by start time
            const aStart = new Date(a.start).getTime();
            const bStart = new Date(b.start).getTime();
            return aStart - bStart;
        });
    }

    // For other views, keep original order or sort by start time
    return [...events].sort((a, b) => {
        const aStart = new Date(a.start).getTime();
        const bStart = new Date(b.start).getTime();
        return aStart - bStart;
    });
};

const getInitialDate = (query: { date?: number }) => {
    try {
        if (query.date && query.date !== 0) {
            const targetDate = parseQueryDate(query.date);
            return targetDate.isValid() ? targetDate.toDate() : moment().toDate();
        }
        return moment().toDate();
    } catch (error) {
        return moment().toDate();
    }
};

const createDayHeaderContent = (arg: any) => {
    return (
        <>
            <div>{arg.date.getDate()}</div>
            <div>{arg.date.toLocaleDateString('it-IT', { weekday: 'long' })}</div>
        </>
    );
};

const getCalendarViews = () => {
    return {
        dayGridMonth: {
            dayHeaderFormat: { weekday: 'long' as const },
            dayMaxEvents: 2 // Show max 2 events, then +n for monthly view
        },
        timeGridWeek: {
            dayHeaderContent: createDayHeaderContent,
            hiddenDays: [],
            dayMaxEvents: 1 // Show max 1 all-day event, then +n for weekly view
        },
        timeGridWorkWeek: {
            type: 'timeGridWeek' as const,
            hiddenDays: [0, 6],
            dayHeaderContent: createDayHeaderContent,
            dayMaxEvents: 1 // Show max 1 all-day event, then +n for work week view
        },
        timeGridDay: {
            dayHeaderContent: createDayHeaderContent,
            dayMaxEvents: 2 // Show max 2 full-day events, then +n for daily view
        }
    };
};

const createViewDidMountHandler = (
    calendarRef: React.MutableRefObject<FullCalendar | null>,
    query: { viewName: string },
    setQuery: React.Dispatch<React.SetStateAction<any>>
) => {
    return function() {
        try {
            const calendarAPI = calendarRef.current?.getApi();
            if (calendarAPI && calendarAPI.view) {
                const viewName = calendarAPI.view.type;
                const internalViewName = gettingCalendarViewName(viewName);
                if (query.viewName !== 'timeGridWorkWeek' && query.viewName !== internalViewName) {
                    setQuery((prevQuery: any) => ({
                        ...prevQuery,
                        viewName: internalViewName
                    }));
                }
            }
        } catch (error) {
            console.error('Error in viewDidMount:', error);
        }
    };
};

const filterEventsByDate = (events: any[], selectedDate: Date | null) => {
    if (!selectedDate || !Array.isArray(events)) {
        return [];
    }

    return events.filter((event: any) => {
        if (!event || !event.start) return false;

        try {
            const eventDate = new Date(event.start);
            return eventDate.getDate() === selectedDate.getDate() &&
                   eventDate.getMonth() === selectedDate.getMonth() &&
                   eventDate.getFullYear() === selectedDate.getFullYear();
        } catch (error) {
            return false;
        }
    });
};

export default function Calendar(props: ICalendarProps) {
    const {
        query,
        setQuery,
        calendarRef,
        fetchEventData,
        eventData,
        DEFAULT_QUERY,
        monthTitle,
        setMonthTitle,
        items,
        t,
        calendarData,
    } = props;

    console.log({calendarData})
    const { modules }: any = useUser();
    const { handleEventDrop } = useHandleEventDrop(fetchEventData, query);
    const navigate = useNavigate();
    const location = useLocation();
    const loggedUserCampiAgenda =
        modules.campi_agenda !== undefined
            ? JSON.parse(modules.campi_agenda)
            : null;

    const impegniMultiutente =
        modules.provisioningRow?.impegni_multiutente || null;

    const netlexSettingsFileId = modules.netlexSettings?.file_id || null;

    const [rightPanelOpen, setRightPanelOpen] = useState(false);
    const [leftPanelOpen, setLeftPanelOpen] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date | null>(null);
    const [moreEventsModalOpen, setMoreEventsModalOpen] = useState(false);
    const [moreEventsData, setMoreEventsData] = useState<{events: any[], date: Date | null}>({events: [], date: null});


    const handleEventResize = (info: any) => handleEventDrop(info);

    const updatedEvents = processEventData(eventData);
    const sortedEvents = sortEventsForDisplay(updatedEvents, query.viewName);

    useEffect(() => {
        try {
            const calendarAPI: any = calendarRef?.current?.getApi();
            if (calendarAPI && query.date && query.date !== 0) {
                const targetDate = parseQueryDate(query.date);
                if (targetDate.isValid()) {
                    calendarAPI.gotoDate(targetDate.toDate());
                } else {
                    console.warn('Invalid target date:', query.date);
                }
            }
        } catch (error) {
            console.error('Error navigating to date:', error);
        }
    }, [query.date]);

    useEffect(() => {
        try {
            const calendarAPI: any = calendarRef?.current?.getApi();
            if (calendarAPI && query.date && query.date !== 0) {
                const targetDate = parseQueryDate(query.date);

                if (targetDate.isValid()) {
                    calendarAPI.gotoDate(targetDate.toDate());
                } else {
                    console.warn('Invalid target date:', query.date);
                }
            }
        } catch (error) {
            console.error('Error navigating to date on calendar ref change:', error);
        }
    }, [calendarRef.current]);

    const handleDateClick = (arg: any) => {
        if (selectedDate &&
            selectedDate.getDate() === arg.date.getDate() &&
            selectedDate.getMonth() === arg.date.getMonth() &&
            selectedDate.getFullYear() === arg.date.getFullYear()) {
            setRightPanelOpen(!rightPanelOpen);
        } else {
            setSelectedDate(arg.date);
            setRightPanelOpen(true);
        }
    };

    // Add event listeners for more links after calendar renders
    useEffect(() => {
        const handleMoreLinkClick = (event: Event) => {
            event.preventDefault();
            event.stopPropagation();

            const target = event.target as HTMLElement;
            const dayCell = target.closest('.fc-daygrid-day');

            if (dayCell) {
                // Get the date from the day cell
                const dateAttr = dayCell.getAttribute('data-date');
                const clickDate = dateAttr ? new Date(dateAttr) : new Date();

                // Get all events for this date from the processed events
                const eventsForDate = sortedEvents.filter((event: any) => {
                    if (!event.start) return false;
                    const eventDate = new Date(event.start);
                    return eventDate.getDate() === clickDate.getDate() &&
                           eventDate.getMonth() === clickDate.getMonth() &&
                           eventDate.getFullYear() === clickDate.getFullYear();
                });

                setMoreEventsData({
                    events: eventsForDate,
                    date: clickDate
                });
                setMoreEventsModalOpen(true);
            }
        };

        // Use a more specific approach - wait for calendar to be fully rendered
        const attachListeners = () => {
            const moreLinks = document.querySelectorAll('.fc-more-link');
            moreLinks.forEach(link => {
                // Remove any existing listeners first
                link.removeEventListener('click', handleMoreLinkClick);
                // Add our custom listener
                link.addEventListener('click', handleMoreLinkClick, true); // Use capture phase
            });
        };

        // Attach listeners after calendar renders
        const timer = setTimeout(attachListeners, 200);

        // Re-attach when calendar view changes
        const observer = new MutationObserver(() => {
            setTimeout(attachListeners, 100);
        });

        const calendarElement = document.querySelector('.fc');
        if (calendarElement) {
            observer.observe(calendarElement, {
                childList: true,
                subtree: true,
                attributes: false
            });
        }

        return () => {
            clearTimeout(timer);
            observer.disconnect();
        };
    }, [sortedEvents]);



    return (
        <>
            <CalendarNavigationButtons
                query={query}
                setQuery={setQuery}
                calendarRef={calendarRef}
                fetchEventData={fetchEventData}
                DEFAULT_QUERY={DEFAULT_QUERY}
                monthTitle={monthTitle}
                setMonthTitle={setMonthTitle}
                t={t}
                leftPanelOpen={leftPanelOpen}
                setLeftPanelOpen={setLeftPanelOpen}
                rightPanelOpen={rightPanelOpen}
                setRightPanelOpen={setRightPanelOpen}
                selectedView={query.viewName}
                onViewChange={(viewName: string) => {
                    setQuery({ ...query, viewName });
                }}
            >
                <CalendarActionButtons
                    query={query}
                    setQuery={setQuery}
                    calendarRef={calendarRef}
                    fetchEventData={fetchEventData}
                    DEFAULT_QUERY={DEFAULT_QUERY}
                    setMonthTitle={setMonthTitle}
                    t={t}
                    leftPanelOpen={leftPanelOpen}
                    setLeftPanelOpen={setLeftPanelOpen}
                    rightPanelOpen={rightPanelOpen}
                    setRightPanelOpen={setRightPanelOpen}
                    selectedView={query.viewName}
                    onViewChange={(viewName: string) => {
                        setQuery({ ...query, viewName });
                    }}
                    calendarData={calendarData}
                    eventData={eventData}
                />
            </CalendarNavigationButtons>

            <Grid container spacing={3}>
                {/* Left Panel */}
                {leftPanelOpen && (
                    <Grid item md={leftPanelOpen ? 3 : 0} sx={{ transition: 'all 0.3s ease', overflow: 'hidden' }}>
                        <Filters
                            DEFAULT_QUERY={DEFAULT_QUERY}
                            query={query}
                            setQuery={setQuery}
                            fetchEventData={fetchEventData}
                            calendarData={calendarData}
                            calendarRef={calendarRef}
                            setMonthTitle={setMonthTitle}
                            t={t}
                        />
                    </Grid>
                )}

                {/* Middle Panel */}
                <Grid item md={leftPanelOpen && rightPanelOpen ? 6 : (leftPanelOpen || rightPanelOpen ? 9 : 12)}>
                    <FullCalendar
                        initialView={gettingCalendarView(query.viewName)}
                        initialDate={getInitialDate(query)}
                        plugins={[
                            dayGridPlugin,
                            timeGridPlugin,
                            listPlugin,
                            interactionPlugin,
                        ]}
                        headerToolbar={false}
                        handleWindowResize={true}
                        height="76vh"
                        expandRows={true}
                        stickyHeaderDates={true}
                        allDaySlot={true}
                        allDayText=""
                        slotMinTime="08:00:00"
                        slotMaxTime="20:00:00"
                        slotLabelFormat={{
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        }}
                        dayHeaderFormat={{ weekday: 'long', day: 'numeric' }}
                        ref={calendarRef}
                        locales={[itLocale]}
                        locale="it"
                        dateClick={handleDateClick}
                        events={getValidatedEventsForCalendar(sortedEvents)}
                        eventDrop={handleEventDrop}
                        eventResize={handleEventResize}
                        slotDuration="00:30:00"
                        eventDisplay="block"
                        fixedWeekCount={false}
                        showNonCurrentDates={true}
                        aspectRatio={1.35}
                        slotEventOverlap={false}
                        eventOverlap={false}
                        moreLinkClick={() => ""}
                        moreLinkContent={(args) => {
                            return `+${args.num}`;
                        }}
                        views={getCalendarViews()}
                        displayEventTime={false}
                        displayEventEnd={false}
                        eventContent={(event: any) =>
                            CustomEventUI({
                                event,
                                items,
                                query,
                                loggedUserCampiAgenda,
                                impegniMultiutente,
                                netlexSettingsFileId,
                                navigate,
                                location,
                            })
                        }
                        viewDidMount={createViewDidMountHandler(calendarRef, query, setQuery)}
                    />
                </Grid>

                {/* Right Panel */}
                {rightPanelOpen && (
                    <Grid item md={rightPanelOpen ? 3 : 0} sx={{ transition: 'all 0.3s ease', overflow: 'hidden' }}>
                        <DayEvents
                            selectedDate={selectedDate}
                            events={filterEventsByDate(sortedEvents, selectedDate)}
                            setRightPanelOpen={setRightPanelOpen}
                            t={t}
                        />
                    </Grid>
                )}

            </Grid>

            {/* More Events Modal */}
            <MoreEventsModal
                open={moreEventsModalOpen}
                onClose={() => setMoreEventsModalOpen(false)}
                events={moreEventsData.events}
                date={moreEventsData.date}
                query={query}
                loggedUserCampiAgenda={loggedUserCampiAgenda}
                impegniMultiutente={impegniMultiutente}
                netlexSettingsFileId={netlexSettingsFileId}
            />
        </>
    );
}
