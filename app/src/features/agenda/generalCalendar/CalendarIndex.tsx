// import { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import Filters from "./filters";
import Calendar from "./calendar";
import useGetEvents from "./hooks/useGetEventsHooks";
import { useTranslation } from "@1f/react-sdk";
// import usePostCustom from "../../../hooks/usePostCustom";

export default function AgendaIndex() {
    const { t } = useTranslation();
    const {
        calendarRef,
        query,
        setQuery,
        eventData,
        fetchEventData,
        DEFAULT_QUERY,
        calendarData,
        monthTitle,
        items,
        setMonthTitle,
    } = useGetEvents();


    return (
        <VaporPage>
            <PageTitle
                title="Calendario Generale"
                actionButtons={[
                    { label: t("Stampa"), onclick: () => "" },
                    {
                        label: t("Aggiungi"),
                        variant: "contained",
                        onclick: () => "",
                    },
                ]}
            />
            <VaporPage.Section>
                <Filters
                    DEFAULT_QUERY={DEFAULT_QUERY}
                    query={query}
                    setQuery={setQuery}
                    fetchEventData={fetchEventData}
                    calendarData={calendarData}
                    calendarRef={calendarRef}
                    t={t}
                    setMonthTitle={setMonthTitle}
                />
            </VaporPage.Section>

            <VaporPage.Section>
                <Calendar
                    DEFAULT_QUERY={DEFAULT_QUERY}
                    calendarRef={calendarRef}
                    query={query}
                    setQuery={setQuery}
                    eventData={eventData}
                    fetchEventData={fetchEventData}
                    monthTitle={monthTitle}
                    setMonthTitle={setMonthTitle}
                    items={items}
                    t={t}
                    calendarData={calendarData}
                />
            </VaporPage.Section>
        </VaporPage>
    );
}
