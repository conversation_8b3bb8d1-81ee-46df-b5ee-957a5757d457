import React from "react";
import { renderCalendarText } from "./calendarHelper";
import { EventTooltip } from "./tooltipCustomHelper";
import { Typography, Grid } from "@vapor/react-material";
import { ICustomEventUIProps } from "../typings/generalCalendar.interface";
import GoogleIcon from "./../../../../assets/images/bulletGoogle.png";
import OutlookIcon from "./../../../../assets/images/bulletOutlook.png";

export const CustomEventUI = (props: ICustomEventUIProps) => {
    const {
        event,
        query,
        loggedUserCampiAgenda,
        impegniMultiutente,
        netlexSettingsFileId,
        navigate,
        location,
        items,
    } = props;

    let dataToRender = renderCalendarText(
        event,
        query.calendarReferent,
        loggedUserCampiAgenda,
        impegniMultiutente,
        netlexSettingsFileId
    );

    const eventStyle = {
        backgroundColor: event.backgroundColor,
        whiteSpace: "normal",
        color: "black",
    };

    const isFullDayEvent = event.event._def.allDay || event.event.allDay;

    const handleNavigateToEvent = () => {
        const event_id = event.event._def.extendedProps.uniqueid;
        const typeEvent = event.event._def.extendedProps.type;

        if (typeEvent === "hearing" && event_id) {
            navigate(`/agenda/agenda/update/${event_id}`, {
                state: {
                    origin: "agenda",
                    type: "update",
                    uniqueId: event_id,
                    items: items,
                    row: { praticaUid: event.event._def.extendedProps.fileUid },
                    prevPath: location.pathname ?? "/calendar/calendar",
                    defaultParams: query,
                    rowDataUrl: 'deadlines',
                },
            });
        } else if (event_id) {
            navigate(`/impegno/update/${event_id}`, {
                state: {
                    prevPath: location.pathname ?? "/calendar/calendar",
                    defaultParams: query,
                },
            });
        }
    };
    return (
        <EventTooltip
            title={
                <React.Fragment>
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            {event?.event?._def.extendedProps.type ===
                            "polisweb" ? (
                                <Typography variant="caption">
                                    {event.event._def.title}
                                </Typography>
                            ) : (
                                <Typography variant="caption">
                                    {!isFullDayEvent && (
                                        <>
                                            <b>{event.event._def.extendedProps.ora}</b>{" "}
                                        </>
                                    )}
                                    {event.event._def.title}
                                </Typography>
                            )}
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sx={{ display: "flex", flexDirection: "column" }}
                        >
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: dataToRender.tooltip,
                                }}
                            />
                        </Grid>
                    </Grid>
                </React.Fragment>
            }
        >
            <div
                style={{
                    ...eventStyle,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
                onClick={handleNavigateToEvent}
            >
                <div>
                    <span
                        dangerouslySetInnerHTML={{ __html: dataToRender.icons }}
                    />
                    {!isFullDayEvent && (
                        <b>{event.event.extendedProps.ora} </b>
                    )}
                    <i>{event.event.title}</i>
                </div>
                {typeof event.event.extendedProps.calendarioEsternoType !==
                    "undefined" &&
                    event.event.extendedProps.calendarioEsternoType !== "" &&
                    event.event.extendedProps.calendarioEsternoType !==
                        null && (
                        <img
                            style={{
                                width: "19px",
                                height: "16px",
                                marginLeft: "8px",
                            }}
                            src={
                                event.event.extendedProps
                                    .calendarioEsternoType === "1"
                                    ? GoogleIcon
                                    : event.event.extendedProps
                                          .calendarioEsternoType === "2"
                                    ? OutlookIcon
                                    : ""
                            }
                        />
                    )}
            </div>
        </EventTooltip>
    );
};
