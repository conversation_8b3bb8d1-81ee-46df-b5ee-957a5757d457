import { useRef, useState, useEffect } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import AnagraficheSidebar from "./sections/AnagraficheSidebar";
import UserData from "./sections/UserData";
import { Grid } from "@vapor/react-material";
import { AnagraficheProvider } from "./providers/AnagraficheProvider";
import { useAnagraficheProvider } from "./providers/AnagraficheProvider";
import Spinner from "../../custom-components/Spinner";
import { useAnagraficheFormHook } from "./hooks/useAnagraficheFormHook";
import ListData from "./sections/ListData";

const ContentWrapper = (props: any) => {
    const { refs, scrollToSection, currentSection } = props;
    const { t } = useTranslation();
    const { userType, setUserType, method, showCreateForm, setShowCreateForm } = useAnagraficheFormHook();
    const { isInitialized, anagrafiche } = useAnagraficheProvider();
    const { customer, groupTypologies } = anagrafiche;

    const findObjectByTypologyId = (id: string) => {
        return groupTypologies?.find((item: any) => item.typologies.some((typology: any) => typology.id === id));
    };
    function capitalizeFirstLetter(val: string) {
        return String(val).charAt(0).toUpperCase() + String(val).slice(1);
    }

    useEffect(() => {
        if (customer?.tipoid !== undefined) {
            const dataFind = findObjectByTypologyId(customer?.tipoid);
            setUserType({
                ...userType,
                details: dataFind?.typologies,
                detailSelected: customer?.tipoid,
                parentSelected: dataFind?.id,
                type: capitalizeFirstLetter(dataFind?.name)
            });
        }
    }, [groupTypologies, customer]);

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <>
            <PageTitle
                title={t("Anagrafica")}
                breadcrumbs={[
                    {
                        label: t("Gestione Anagrafiche"),
                        active: true,
                        path: "/anagrafiche"
                    },
                    {
                        label: customer.nome,
                        active: true,
                        path: "/anagrafiche"
                    }
                ]}
                pathToPrevPage={"/anagrafiche"}
            />
            <VaporPage.Section>
                <Grid container spacing={2}>
                    <Grid
                        item
                        xs={12}
                        sm={2}
                        md={2}
                        style={{
                            borderRight: "hsl(200, 20%, 77%) solid 1px",
                            marginTop: "inherit",
                            marginLeft: "inherit",
                            backgroundColor: "#f7f7f7",
                            height: "auto"
                        }}
                    >
                        <AnagraficheSidebar scrollToSection={scrollToSection} refs={refs} currentSection={currentSection} userType={userType} />
                    </Grid>
                    <Grid item xs={12} sm={10} md={10} sx={{ width: "100%", flexGrow: 1 }}>
                        {props.children({
                            userType,
                            setUserType,
                            method,
                            showCreateForm,
                            setShowCreateForm
                        })}
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </>
    );
};

const AnagraficheDetail = () => {
    const parentRef = useRef<HTMLDivElement>(null);
    const datiGeneraliRef = useRef<HTMLDivElement>(null);
    const recapitiRef = useRef<HTMLDivElement>(null);
    const contactsRefs = useRef<HTMLDivElement>(null);
    const groupsRefs = useRef<HTMLDivElement>(null);
    const noteprofilazioneRefs = useRef<HTMLDivElement>(null);
    const cameraDiCommercioRefs = useRef<HTMLDivElement>(null);
    const fiscaliBancheRefs = useRef<HTMLDivElement>(null);
    const antiriciclaggioRefs = useRef<HTMLDivElement>(null);

    const praticheRefs = useRef<HTMLDivElement>(null);
    const contrattiRefs = useRef<HTMLDivElement>(null);
    const documentiRefs = useRef<HTMLDivElement>(null);
    const fatturazioneRefs = useRef<HTMLDivElement>(null);
    const immobiliRefs = useRef<HTMLDivElement>(null);

    const [currentSection, setCurrentSection] = useState<string>("1");
    const [bottomPadding, setBottomPadding] = useState<string>("0"); // Default padding

    const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
        if (parentRef.current && ref.current) {
            setCurrentSection(ref.current.id);

            const parent = parentRef.current;
            const element = ref.current;
            const parentTop = parent?.getBoundingClientRect().top;
            const elementTop = element.getBoundingClientRect().top;
            const scrollTo = elementTop - parentTop + parent.scrollTop;
            setCurrentSection(ref.current.id);
            window.scrollTo({ top: scrollTo, behavior: "smooth" });
        }
    };

    const refs = {
        datiGeneraliRef,
        recapitiRef,
        contactsRefs,
        groupsRefs,
        noteprofilazioneRefs,
        fiscaliBancheRefs,
        cameraDiCommercioRefs,
        antiriciclaggioRefs,
        parentRef,
        praticheRefs,
        contrattiRefs,
        documentiRefs,
        fatturazioneRefs,
        immobiliRefs
    };

    function currentSectionIsUserData(currentSection: string): boolean {
        const userDataSections = ["1", "2", "3", "4", "5", "6", "7", "9"];
        return userDataSections.includes(currentSection);
    }

    return (
        <VaporPage>
            <div>
                <AnagraficheProvider>
                    <ContentWrapper refs={refs} currentSection={currentSection} scrollToSection={scrollToSection}>
                        {({ userType, setUserType, method, showCreateForm, setShowCreateForm }: any) => (
                            <div
                                ref={refs.parentRef}
                                style={{
                                    overflowY: "auto",
                                    scrollbarWidth: "none",
                                    position: "relative",
                                    width: "100%",
                                    paddingBottom: `${bottomPadding}px`
                                }}
                            >
                                {/* Render both UserData and ListData containers and control their visibility */}
                                <div
                                    style={{
                                        display: currentSectionIsUserData(currentSection) ? "block" : "none"
                                    }}
                                >
                                    <UserData refs={refs} setCurrentSection={setCurrentSection} userType={userType} setUserType={setUserType} method={method} showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} setBottomPadding={setBottomPadding} />
                                </div>
                                <div
                                    style={{
                                        display: currentSectionIsUserData(currentSection) ? "none" : "block"
                                    }}
                                >
                                    <ListData refs={refs} currentSection={currentSection} setCurrentSection={setCurrentSection} />
                                </div>
                            </div>
                        )}
                    </ContentWrapper>
                </AnagraficheProvider>
            </div>
        </VaporPage>
    );
};

export default AnagraficheDetail;
