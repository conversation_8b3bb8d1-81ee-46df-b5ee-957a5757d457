import { Box } from "@vapor/react-material";
import {
    IGridColumn,
    IGridSettings,
} from "../../../interfaces/general.interfaces";
import BaseGridList from "../../../models/BaseGridList";
import { mapOtherList } from "../../../utilities/common";


export const getFatturazioneGrid = async (t: any, name: string) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Numero"), t("Data"), t("Tipologia"),t("Motivo"), t("IVA"), t("Totale"), t("Scoperto")],
            column_keys: ["progressivo", "data", "tipologia","motivo", "totale_iva", "totale", "scoperto"],
            column_widths: ["10%", "10%", "20%", "30%", "10%", "10%", "10%"],
            cell_templates: [null, null, null, null],
            sortable: [true, true, true, true, true, true, true],
            header_templates: "",
            column_totals: null,
        },
    });
    
    if (name === "peopleprefees") {
        return mapFatturazionePreavvisiColumnNames(response, name);
    } else  {
        return mapFatturazioneColumnNames(response, name);
    }
};


export const mapFatturazionePreavvisiColumnNames = (
    response: any,
    name: string
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
             if (column_keys[index] === "progressivo") {
                returnColumn.renderCell = (row: any) =>
                    progressivoPreaviso(row);
            }
           else if (column_keys[index] === "scoperto") {
                returnColumn.renderCell = (row: any) =>
                    handleScopertoColumnColor(row);
            } else if (column_keys[index] === "totale") {
                returnColumn.renderCell = (row: any) =>
                    handleTotalColumn(row);
            } else if (column_keys[index] === "tipologia") {
                returnColumn.renderCell = () =>
                    handleTipologiaColumn(name);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapFatturazioneColumnNames = (
    response: any,
    name: string
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
             if (column_keys[index] === "progressivo") {
                returnColumn.renderCell = (row: any) =>
                    numeroColumn(row);
            }
           else if (column_keys[index] === "scoperto") {
                returnColumn.renderCell = (row: any) =>
                    handleScopertoColumnColor(row);
            } else if (column_keys[index] === "totale") {
                returnColumn.renderCell = (row: any) =>
                    handleTotalColumn(row);
            } else if (column_keys[index] === "tipologia") {
                returnColumn.renderCell = () =>
                    handleTipologiaColumn(name);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

const handleScopertoColumnColor = ({ row }: any) => {
    return (
        <Box sx={{ color: "#d12a2a" }}>€ {row?.scoperto}</Box>
    )
}

const handleTipologiaColumn = (name: string) => {
    return (
        <Box>
            {name === "peopleprefees" ? "Preavvisi di parcella" : name === "peoplestandardfees" ? "Fatture" : name === "peoplefees" ? "Fatture elettroniche" : "Nota di credito elettroniche"}
        </Box>
    )
}
const progressivoPreaviso = ({ row }: { row: any }) => {
  let progressivo = row.progressivo ? row.progressivo : '';

  if (progressivo === 0 || progressivo === '0') {
    progressivo = '';
  }

  if (row.progressivo_alfanumerico) {
    progressivo = row.progressivo_alfanumerico;
  }

  if (row.sezionale_progressivo) {
    progressivo = progressivo
      ? `${progressivo}-${row.sezionale_progressivo}`
      : row.sezionale_progressivo;
  }

  if (row.sezionale_progressivo_bis) {
    progressivo += `-${row.sezionale_progressivo_bis}`;
  }

  return progressivo.toString();
};

const handleTotalColumn = ({ row }: any) => {
    return (
        <Box>€ {row?.totale}</Box>
    )
}

const numeroColumn = (row: any) => {
    return (
        <Box>
            {row?.row?.progressivo}
        </Box>
    )
}
